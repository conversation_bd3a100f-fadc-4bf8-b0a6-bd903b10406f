<template>
  <div class="next-section-block">
    <!-- Section separator line -->
    <div class="section-separator">
      <div class="separator-line"></div>
      <div class="separator-content">
        <q-icon name="arrow_forward" size="md" color="primary" class="q-mr-sm" />
        <span class="text-h6 text-primary">Section Navigation</span>
      </div>
      <div class="separator-line"></div>
    </div>

    <!-- Navigation controls -->
    <div class="navigation-controls">
      <div class="control-label">
        <q-icon name="settings" size="sm" class="q-mr-xs" />
        After completing section {{ currentSection }}:
      </div>

      <q-select
        :model-value="selectedNextSection"
        :options="sectionDropdownOptions"
        emit-value
        map-options
        outlined
        dense
        placeholder="Continue to next section"
        style="min-width: 350px"
        @update:model-value="handleSectionSelect"
        :loading="isUpdating"
        :disable="isUpdating"
        class="navigation-select"
      >
        <template #selected>
          <div class="selected-option">
            <q-icon name="navigate_next" size="sm" class="q-mr-xs" />
            <span v-if="selectedNextSection">{{ getSelectedSectionLabel() }}</span>
            <span v-else class="text-grey-6">Continue to next section</span>
          </div>
        </template>

        <template #option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section avatar>
              <q-icon name="navigate_next" size="sm" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ scope.opt.label }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>

        <template #append>
          <q-icon name="keyboard_arrow_down" />
        </template>
      </q-select>

      <q-spinner v-if="isUpdating" size="sm" color="primary" class="q-ml-sm" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, inject, onMounted } from 'vue';
import type { ItemBlock } from 'src/types/models';
import type { ItemBlockStore } from 'src/stores/item_block_store';
import { OptionService } from 'src/services/asm/optionService';
import { debounce } from 'quasar';

// Get the store instance provided by the parent ItemBlock
const store = inject<ItemBlockStore>('blockStore');
if (!store) {
  throw new Error('NextSectionItemBlock must be used within an ItemBlock component');
}

// Props
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
  showSectionDropdowns?: boolean;
  availableSections?: { label: string; value: number; headerTitle?: string }[];
}>();

// Emits
const emit = defineEmits<{
  'update:option-next-section': [optionId: number, nextSection: number | null];
}>();

// State
const isUpdating = ref(false);
const optionService = new OptionService();

// Get current section from itemBlock
const currentSection = computed(() => props.itemBlock.section);

// Get the first (and should be only) option for NEXTSECTION type
const currentOption = computed(() => {
  return props.itemBlock.options?.[0];
});

// Get selected next section value
const selectedNextSection = computed(() => {
  return currentOption.value?.nextSection || null;
});

// Computed property for section dropdown options with Thai formatting
const sectionDropdownOptions = computed(() => {
  if (!props.availableSections || props.availableSections.length === 0) return [];

  // Add default option first
  const options: { label: string; value: number | null }[] = [
    {
      label: 'Continue to next section',
      value: null,
    },
  ];

  // Add section options with Thai formatting
  props.availableSections.forEach((section) => {
    const headerTitle = section.headerTitle || '';
    const label = headerTitle
      ? `Go to section ${section.value} (${headerTitle})`
      : `Go to section ${section.value}`;

    options.push({
      label: label,
      value: section.value,
    });
  });

  return options;
});

// Get selected section label for display
const getSelectedSectionLabel = (): string => {
  if (!selectedNextSection.value) return 'Continue to next section';

  const selectedOption = sectionDropdownOptions.value.find(
    (option) => option.value === selectedNextSection.value,
  );

  return selectedOption?.label || `Go to section ${selectedNextSection.value}`;
};

// Debounced update function
const debouncedUpdate = debounce(async (optionId: number, nextSection: number | null) => {
  try {
    isUpdating.value = true;

    await optionService.updateOptionSilent(optionId, {
      itemBlockId: props.itemBlock.id,
      nextSection: nextSection,
    });

    // Emit the update to parent
    emit('update:option-next-section', optionId, nextSection);

    console.log('✅ NextSection updated successfully:', { optionId, nextSection });
  } catch (error) {
    console.error('❌ Failed to update nextSection:', error);
  } finally {
    isUpdating.value = false;
  }
}, 1000);

// Handle section selection
const handleSectionSelect = (value: number | null) => {
  if (!currentOption.value) {
    console.error('No option found for NEXTSECTION block');
    return;
  }

  console.log('🔄 Updating nextSection:', {
    optionId: currentOption.value.id,
    oldValue: selectedNextSection.value,
    newValue: value,
  });

  debouncedUpdate(currentOption.value.id, value);
};

// Initialize component
onMounted(() => {
  console.log('NextSectionItemBlock mounted:', {
    itemBlockId: props.itemBlock.id,
    currentSection: currentSection.value,
    hasOption: !!currentOption.value,
    optionId: currentOption.value?.id,
    currentNextSection: selectedNextSection.value,
    availableSections: props.availableSections?.length || 0,
  });
});
</script>

<style scoped>
.next-section-block {
  margin: 24px 0;
  padding: 0;
  background: transparent;
}

.section-separator {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 16px;
}

.separator-line {
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, transparent, #1976d2, transparent);
  opacity: 0.3;
}

.separator-content {
  display: flex;
  align-items: center;
  padding: 0 16px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
  border: 2px solid #e3f2fd;
}

.navigation-controls {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin: 0 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.control-label {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #495057;
  margin-bottom: 12px;
  font-size: 14px;
}

.navigation-select {
  background-color: white;
  border-radius: 8px;
}

.navigation-select .q-field__control {
  border-radius: 8px;
}

.selected-option {
  display: flex;
  align-items: center;
}

/* Hover effect for the entire block */
.next-section-block:hover .navigation-controls {
  border-color: #1976d2;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
  transition: all 0.2s ease;
}

.next-section-block:hover .separator-content {
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
  border-color: #1976d2;
  transition: all 0.2s ease;
}
</style>
