<template>
  <div class="next-section-block">
    <div class="row items-center q-gutter-md">
      <div class="text-body2 text-grey-8">After section {{ currentSection }}</div>

      <q-select
        :model-value="selectedNextSection"
        :options="sectionDropdownOptions"
        emit-value
        map-options
        outlined
        dense
        placeholder="Continue to next section"
        style="min-width: 300px"
        @update:model-value="handleSectionSelect"
        :loading="isUpdating"
        :disable="isUpdating"
      >
        <template #selected>
          <span v-if="selectedNextSection">{{ getSelectedSectionLabel() }}</span>
          <span v-else class="text-grey-6">Continue to next section</span>
        </template>

        <template #option="scope">
          <q-item v-bind="scope.itemProps">
            <q-item-section>
              <q-item-label>{{ scope.opt.label }}</q-item-label>
            </q-item-section>
          </q-item>
        </template>

        <template #append>
          <q-icon name="keyboard_arrow_down" />
        </template>
      </q-select>

      <q-spinner v-if="isUpdating" size="sm" color="primary" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, inject, onMounted } from 'vue';
import type { ItemBlock } from 'src/types/models';
import type { ItemBlockStore } from 'src/stores/item_block_store';
import { OptionService } from 'src/services/asm/optionService';
import { debounce } from 'quasar';

// Get the store instance provided by the parent ItemBlock
const store = inject<ItemBlockStore>('blockStore');
if (!store) {
  throw new Error('NextSectionItemBlock must be used within an ItemBlock component');
}

// Props
const props = defineProps<{
  itemBlock: ItemBlock;
  type: 'quiz' | 'evaluate';
  showSectionDropdowns?: boolean;
  availableSections?: { label: string; value: number; headerTitle?: string }[];
}>();

// Emits
const emit = defineEmits<{
  'update:option-next-section': [optionId: number, nextSection: number | null];
}>();

// State
const isUpdating = ref(false);
const optionService = new OptionService();

// Get current section from itemBlock
const currentSection = computed(() => props.itemBlock.section);

// Get the first (and should be only) option for NEXTSECTION type
const currentOption = computed(() => {
  return props.itemBlock.options?.[0];
});

// Get selected next section value
const selectedNextSection = computed(() => {
  return currentOption.value?.nextSection || null;
});

// Computed property for section dropdown options with Thai formatting
const sectionDropdownOptions = computed(() => {
  if (!props.availableSections || props.availableSections.length === 0) return [];

  // Add default option first
  const options: { label: string; value: number | null }[] = [
    {
      label: 'Continue to next section',
      value: null,
    },
  ];

  // Add section options with Thai formatting
  props.availableSections.forEach((section) => {
    const headerTitle = section.headerTitle || '';
    const label = headerTitle
      ? `Go to section ${section.value} (${headerTitle})`
      : `Go to section ${section.value}`;

    options.push({
      label: label,
      value: section.value,
    });
  });

  return options;
});

// Get selected section label for display
const getSelectedSectionLabel = (): string => {
  if (!selectedNextSection.value) return 'Continue to next section';

  const selectedOption = sectionDropdownOptions.value.find(
    (option) => option.value === selectedNextSection.value,
  );

  return selectedOption?.label || `Go to section ${selectedNextSection.value}`;
};

// Debounced update function
const debouncedUpdate = debounce(async (optionId: number, nextSection: number | null) => {
  try {
    isUpdating.value = true;

    await optionService.updateOptionSilent(optionId, {
      itemBlockId: props.itemBlock.id,
      nextSection: nextSection,
    });

    // Emit the update to parent
    emit('update:option-next-section', optionId, nextSection);

    console.log('✅ NextSection updated successfully:', { optionId, nextSection });
  } catch (error) {
    console.error('❌ Failed to update nextSection:', error);
  } finally {
    isUpdating.value = false;
  }
}, 1000);

// Handle section selection
const handleSectionSelect = (value: number | null) => {
  if (!currentOption.value) {
    console.error('No option found for NEXTSECTION block');
    return;
  }

  console.log('🔄 Updating nextSection:', {
    optionId: currentOption.value.id,
    oldValue: selectedNextSection.value,
    newValue: value,
  });

  debouncedUpdate(currentOption.value.id, value);
};

// Initialize component
onMounted(() => {
  console.log('NextSectionItemBlock mounted:', {
    itemBlockId: props.itemBlock.id,
    currentSection: currentSection.value,
    hasOption: !!currentOption.value,
    optionId: currentOption.value?.id,
    currentNextSection: selectedNextSection.value,
    availableSections: props.availableSections?.length || 0,
  });
});
</script>

<style scoped>
.next-section-block {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;
}

.next-section-block .q-select {
  background-color: white;
}
</style>
