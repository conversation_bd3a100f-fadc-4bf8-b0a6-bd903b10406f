import { defineStore } from 'pinia';
import type { ItemBlock, Assessment } from 'src/types/models';
import type { DataResponse } from 'src/types/data';
import { ref, computed, nextTick } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { getCurrentSection } from 'src/utils/block_helper';
import { useGlobalStore } from './global';

type DOMRefElement = Element | null;

// ===== ค่าคงที่ =====
const ALLOWED_BLOCK_TYPES: string[] = ['RADIO', 'CHECKBOX', 'TEXTFIELD', 'GRID'];
const FAB_POSITION_DEBOUNCE_DELAY = 50;
const FAB_LOCK_RELEASE_DELAY = 200;
const FAB_PROTECTION_DURATION = 200;
const UI_REFRESH_ITERATIONS = 5;
const DELAYED_REFRESH_DELAYS = [200, 100, 100];

export const useBlockCreatorStore = defineStore('blockCreator', () => {
  // ===== สถานะการจัดการบล็อก =====
  const blockList = ref<ItemBlock[]>([]);
  const blockDOMReferences: Record<number, DOMRefElement> = {};
  const currentSelectedBlock = ref<ItemBlock | null>(null);
  const currentSelectedBlockId = ref<string | undefined>();

  // ===== สถานะการจัดการแบบประเมิน =====
  const assessmentMetadata = ref<DataResponse<Assessment> | null>(null);
  const assessmentCollection = ref<Assessment[]>([]);
  const activeAssessment = ref<Assessment | null>(null);
  const isLoadingAssessment = ref(false);
  const assessmentError = ref<string | null>(null);
  const paginationPage = ref(1);
  const paginationLimit = ref(5);
  const searchQuery = ref('');

  // ===== สถานะการโต้ตอบ UI =====
  const showDuplicateDialog = ref(false);
  const isBlockCreationInProgress = ref(false);
  const isDragOperationActive = ref(false);
  const uiRefreshTrigger = ref(0);

  // ===== การแบ่งหน้า ItemBlock =====
  const itemBlockPage = ref(1);
  const hasMoreBlocks = ref(true);

  // function addBlocks(newBlocks: ItemBlock[]) {
  //   blockList.value.push(...newBlocks);
  // }
  function addBlocks(newBlocks: ItemBlock[]) {
    blockList.value = [...blockList.value, ...newBlocks];
  }

  // ===== สถานะการจัดตำแหน่ง FAB =====
  const fabPositioningState = ref({
    isPositionLocked: false,
    pendingTargetPosition: null as number | null,
    isCreationInProgress: false,
    targetBlockId: null as number | null,
  });

  // ===== สถานะการสร้างรหัส ID =====
  const questionIdCounter = ref(0);
  const optionIdCounter = ref(0);

  // ===== ยูทิลิตี้สร้างรหัส ID =====
  function generateNextQuestionId(): number {
    questionIdCounter.value++;
    return questionIdCounter.value;
  }

  function generateNextOptionId(): number {
    optionIdCounter.value++;
    return optionIdCounter.value;
  }

  // ===== ค่าคำนวณสถานะ FAB =====
  const isFabPositionLocked = computed({
    get: () => fabPositioningState.value.isPositionLocked,
    set: (value: boolean) => {
      fabPositioningState.value.isPositionLocked = value;
    },
  });

  // async function loadMoreBlocks(assessmentId: number, type: 'quiz' | 'evaluate') {
  //   if (!hasMoreBlocks.value) return;

  //   const service = new AssessmentService(type);
  //   const nextPage = itemBlockPage.value + 1;
  //   const limit = 10;

  //   const { pagedItemBlocks } = await service.fetchOne(assessmentId, {
  //     page: nextPage,
  //     limit,
  //   });

  //   if (pagedItemBlocks.length < limit) {
  //     hasMoreBlocks.value = false;
  //   }

  //   itemBlockPage.value = nextPage;

  //   // ดึง blocks ปัจจุบันมาแล้วต่อท้าย
  //   blockList.value = [...blockList.value, ...pagedItemBlocks];
  // }

  async function loadMoreBlocks(assessmentId: number, type: 'quiz' | 'evaluate') {
    if (!hasMoreBlocks.value) return;

    const service = new AssessmentService(type);
    const nextPage = itemBlockPage.value + 1;
    const limit = 10;

    const { pagedItemBlocks } = await service.fetchOne(assessmentId, {
      page: nextPage,
      limit,
    });

    // อัปเดตสถานะโหลดต่อ
    if (pagedItemBlocks.length < limit) {
      hasMoreBlocks.value = false;
    }

    // เพิ่มหน้า
    itemBlockPage.value = nextPage;

    // ต่อท้าย blocks ที่มีอยู่
    blockList.value = [...(blockList.value || []), ...pagedItemBlocks];
    // ✅ concat ชัดเจน
  }

  function updateOptionTextInStore(optionId: number, newText: string) {
    blockList.value.forEach((block) => {
      if (block.options) {
        const option = block.options.find((opt) => opt.id === optionId);
        if (option) {
          option.optionText = newText;
        }
      }
    });

    // sync ไปใน activeAssessment ด้วย
    if (activeAssessment.value?.itemBlocks) {
      activeAssessment.value.itemBlocks.forEach((block) => {
        if (block.options) {
          const option = block.options.find((opt) => opt.id === optionId);
          if (option) {
            option.optionText = newText;
          }
        }
      });
    }
  }

  const pendingFabTargetPosition = computed({
    get: () => fabPositioningState.value.pendingTargetPosition,
    set: (value: number | null) => {
      fabPositioningState.value.pendingTargetPosition = value;
    },
  });

  const isFabCreationInProgress = computed({
    get: () => fabPositioningState.value.isCreationInProgress,
    set: (value: boolean) => {
      fabPositioningState.value.isCreationInProgress = value;
    },
  });

  const fabTargetBlockId = computed({
    get: () => fabPositioningState.value.targetBlockId,
    set: (value: number | null) => {
      fabPositioningState.value.targetBlockId = value;
    },
  });

  // ===== ยูทิลิตี้จัดการ Timeout =====
  const fabTimeoutManager = {
    fabPositionTimeout: null as NodeJS.Timeout | null,
    lockReleaseTimeout: null as NodeJS.Timeout | null,

    clearFabPositionTimeout() {
      if (this.fabPositionTimeout) {
        clearTimeout(this.fabPositionTimeout);
        this.fabPositionTimeout = null;
      }
    },

    clearLockReleaseTimeout() {
      if (this.lockReleaseTimeout) {
        clearTimeout(this.lockReleaseTimeout);
        this.lockReleaseTimeout = null;
      }
    },

    clearAllTimeouts() {
      this.clearFabPositionTimeout();
      this.clearLockReleaseTimeout();
    },
  };

  // ===== การจัดการ DOM Reference ของ Block =====
  function setBlockDOMReference(blockId: number, element: DOMRefElement) {
    blockDOMReferences[blockId] = element;
  }

  function getBlockDOMReference(blockId: number) {
    return blockDOMReferences[blockId];
  }

  // ===== การดำเนินการกับ Block =====
  function insertBlockAfterIndex(newBlock: ItemBlock, targetIndex: number) {
    blockList.value.splice(targetIndex + 1, 0, newBlock);
  }

  function addBlockToEnd(newBlock: ItemBlock) {
    blockList.value.push(newBlock);
  }

  function reorderBlocksAndUpdateSequences(reorderedBlocks: ItemBlock[]) {
    blockList.value = reorderedBlocks;
    updateAllBlockSequences();
    synchronizeAssessmentWithBlocks();
  }

  function updateBlockSection(sectionNumber: number, blockIndex: number) {
    blockList.value[blockIndex] = {
      ...blockList.value[blockIndex],
      section: sectionNumber,
    } as ItemBlock;
  }

  function replaceBlockAtIndex(updatedBlock: ItemBlock, targetIndex: number) {
    blockList.value[targetIndex] = updatedBlock;
  }

  function createDuplicateBlock(sourceBlock: ItemBlock, insertionIndex: number) {
    const duplicatedBlock: ItemBlock = { ...sourceBlock };
    blockList.value.splice(insertionIndex + 1, 0, duplicatedBlock);
    return duplicatedBlock;
  }

  // ===== ยูทิลิตี้จัดการลำดับ (Sequence) =====
  function updateAllBlockSequences() {
    blockList.value.forEach((block, index) => {
      block.sequence = index + 1;
    });
  }

  // ฟังก์ชันใหม่สำหรับแทรกบล็อกและจัดการลำดับ
  function insertBlockWithSequenceManagement(newBlock: ItemBlock, targetIndex: number) {
    // ตรวจสอบ index
    if (targetIndex < 0 || targetIndex >= blockList.value.length) {
      // ถ้า index ไม่ถูกต้อง ให้เพิ่มท้าย
      newBlock.sequence = blockList.value.length + 1;
      blockList.value.push(newBlock);
      return;
    }

    const currentBlock = blockList.value[targetIndex];
    if (!currentBlock) {
      // ถ้าไม่มี block ปัจจุบัน ให้เพิ่มท้าย
      newBlock.sequence = blockList.value.length + 1;
      blockList.value.push(newBlock);
      return;
    }

    const insertSequence = currentBlock.sequence + 1;

    // อัปเดตลำดับของบล็อกที่อยู่หลังจุดแทรก
    blockList.value.forEach((block) => {
      if (block.sequence >= insertSequence) {
        block.sequence = block.sequence + 1;
      }
    });

    // กำหนดลำดับให้บล็อกใหม่
    newBlock.sequence = insertSequence;

    // แทรกบล็อกในตำแหน่งที่ถูกต้อง (หลัง block ปัจจุบัน)
    blockList.value.splice(targetIndex + 1, 0, newBlock);
  }

  // ฟังก์ชันสำหรับซิงค์ลำดับกับ backend หลังแทรก
  async function syncSequencesToBackend(assessmentType: 'quiz' | 'evaluate') {
    try {
      // ซิงค์เฉพาะบล็อกที่มี id (สร้างใน backend แล้ว)
      const validBlocks = blockList.value.filter((block) => {
        return block.id && !isNaN(Number(block.id)) && Number(block.id) > 0;
      });

      if (validBlocks.length === 0) {
        console.log('🔄 ไม่มีบล็อกที่ต้องซิงค์ลำดับ');
        return;
      }

      console.log('🔄 กำลังซิงค์ลำดับไป backend:', {
        blocksCount: validBlocks.length,
        sequences: validBlocks.map((b) => ({ id: b.id, sequence: b.sequence })),
      });

      const assessmentService = new AssessmentService(assessmentType);
      const result = await assessmentService.updateBlockSequences(validBlocks);

      if (result?.success) {
        console.log('✅ ซิงค์ลำดับสำเร็จ');
      } else {
        console.warn('⚠️ ผลลัพธ์การซิงค์ลำดับไม่คาดคิด:', result);
      }
    } catch (error) {
      console.error('❌ ซิงค์ลำดับไป backend ไม่สำเร็จ:', error);
      // ไม่ throw error เพื่อไม่ให้ UI พัง - state local ยังอัปเดตอยู่
    }
  }

  function synchronizeAssessmentWithBlocks() {
    if (activeAssessment.value && activeAssessment.value.itemBlocks) {
      activeAssessment.value.itemBlocks = [...blockList.value];
    }
  }

  // ===== เครื่องมือรีเฟรช UI =====
  async function executeBasicUIRefresh() {
    uiRefreshTrigger.value++;
    await nextTick();
    uiRefreshTrigger.value++;
    await nextTick();
  }

  async function executeComplexUIRefresh() {
    await executeBasicUIRefresh();

    if (activeAssessment.value) {
      activeAssessment.value.itemBlocks = [...blockList.value];
      await nextTick();
    }

    // รีเฟรชหลายรอบสำหรับ DOM ที่ซับซ้อน
    for (let i = 0; i < UI_REFRESH_ITERATIONS; i++) {
      uiRefreshTrigger.value++;
      await nextTick();
    }

    // รีเฟรชแบบหน่วงเวลา
    for (const delay of DELAYED_REFRESH_DELAYS) {
      await executeDelayedRefresh(delay);
      await nextTick();
    }
  }

  async function executeDelayedRefresh(delay: number): Promise<void> {
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        uiRefreshTrigger.value++;
        resolve();
      }, delay);
    });
  }

  async function synchronizeBlocksWithAssessment() {
    if (activeAssessment.value && activeAssessment.value.itemBlocks) {
      blockList.value = [...activeAssessment.value.itemBlocks];
      await executeBasicUIRefresh();
    }
  }

  // ===== ยูทิลิตี้จัดการตำแหน่ง FAB =====
  function updateFabPosition(blockId: number, shouldPositionImmediately = false) {
    if (isBlockCreationInProgress.value && !shouldPositionImmediately) {
      pendingFabTargetPosition.value = blockId;
      return;
    }

    fabTimeoutManager.clearFabPositionTimeout();

    if (shouldPositionImmediately) {
      positionFabImmediately(blockId);
    } else {
      positionFabWithDebounce(blockId);
    }
  }

  function positionFabImmediately(blockId: number) {
    isFabPositionLocked.value = true;
    currentSelectedBlockId.value = `block-${blockId}`;
    fabTimeoutManager.clearLockReleaseTimeout();

    fabTimeoutManager.lockReleaseTimeout = setTimeout(() => {
      isFabPositionLocked.value = false;
      const pendingPosition = pendingFabTargetPosition.value;
      if (pendingPosition && pendingPosition !== blockId) {
        updateFabPosition(pendingPosition, false);
        pendingFabTargetPosition.value = null;
      }
    }, FAB_LOCK_RELEASE_DELAY);
  }

  function positionFabWithDebounce(blockId: number) {
    fabTimeoutManager.fabPositionTimeout = setTimeout(() => {
      if (!isFabPositionLocked.value) {
        currentSelectedBlockId.value = `block-${blockId}`;
      } else {
        pendingFabTargetPosition.value = blockId;
      }
    }, FAB_POSITION_DEBOUNCE_DELAY);
  }

  function createFabProtectionForBlock(blockId: number, duration = FAB_PROTECTION_DURATION) {
    isFabCreationInProgress.value = true;
    fabTargetBlockId.value = blockId;
    isFabPositionLocked.value = true;
    currentSelectedBlockId.value = `block-${blockId}`;

    fabTimeoutManager.clearLockReleaseTimeout();

    fabTimeoutManager.lockReleaseTimeout = setTimeout(() => {
      isFabPositionLocked.value = false;
      isFabCreationInProgress.value = false;
      fabTargetBlockId.value = null;
      currentSelectedBlockId.value = `block-${blockId}`;
    }, duration);
  }

  // ===== ยูทิลิตี้ Scroll =====
  function scrollToSelectedBlock() {
    if (!currentSelectedBlockId.value) return;
    const blockId = Number(currentSelectedBlockId.value.split('-')[1]);
    const blockElement = getBlockDOMReference(blockId);
    if (blockElement && 'scrollIntoView' in blockElement) {
      (blockElement as HTMLElement).scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  }

  async function positionFabAndScrollToBlock(blockId: number) {
    updateFabPosition(blockId, true);
    await nextTick();
    await nextTick();
    scrollToSelectedBlock();
  }

  // ===== เครื่องมือการตรวจสอบ =====
  // ===== ยูทิลิตี้ตรวจสอบความถูกต้อง =====
  function validateAssessmentForOperation(operationName: string): boolean {
    if (!activeAssessment.value?.id) {
      console.error(`❌ Assessment ID is required for ${operationName}`);
      return false;
    }
    return true;
  }

  // ===== เครื่องมือการประเมินผล =====
  function getAssessmentId(): number | null {
    return activeAssessment.value?.id || null;
  }

  function getItemBlockById(id: number) {
    return activeAssessment.value?.itemBlocks?.find((block) => block.id === id) || null;
  }

  function getHeaderBlockId(): number | null {
    const headerBlock = activeAssessment.value?.itemBlocks?.find(
      (block) => block.type === 'HEADER',
    );
    return headerBlock?.id || null;
  }

  function getRadioBlockId(): number | null {
    const radioBlock = activeAssessment.value?.itemBlocks?.find((block) => block.type === 'RADIO');
    return radioBlock?.id || null;
  }

  function getAllItemBlockIds(): number[] {
    return activeAssessment.value?.itemBlocks?.map((block) => block.id) || [];
  }

  // ===== เครื่องมือการจัดการหมวดหมู่ =====
  function isSectionBlock(index: number) {
    const block = blockList.value[index];
    return block?.type === 'HEADER' && block.section > 0;
  }

  // ===== ID-based section check to fix sorting issues =====
  function isSectionBlockById(blockId: number): boolean {
    const block = blockList.value.find((b) => b.id === blockId);
    return block?.type === 'HEADER' && block.section > 0;
  }

  const totalSections = computed(() => {
    // Only count headers that represent actual sections (section > 0)
    return blockList.value.filter((block) => block.type === 'HEADER' && block.section > 0).length;
  });

  function getSectionNumber(index: number) {
    let sectionCounter = 0;
    for (let i = 0; i <= index; i++) {
      const block = blockList.value[i];
      // Only count headers that represent actual sections (section > 0)
      if (block?.type === 'HEADER' && block.section > 0) {
        sectionCounter++;
      }
    }
    return sectionCounter;
  }

  // ✅ NEW: Add a sequence-based section number calculation for sorted blocks
  function getSectionNumberBySequence(blockSequence: number): number {
    // Sort blocks by sequence and count sections up to the given sequence
    const sortedBlocks = blockList.value.slice().sort((a, b) => a.sequence - b.sequence);
    let sectionCounter = 0;

    for (const block of sortedBlocks) {
      if (block.sequence > blockSequence) break;
      if (block.type === 'HEADER' && block.section > 0) {
        sectionCounter++;
      }
    }

    return sectionCounter;
  }

  // ✅ NEW: Get section number by block ID
  function getSectionNumberById(blockId: number): number {
    const block = blockList.value.find((b) => b.id === blockId);
    if (!block) return 0;

    return getSectionNumberBySequence(block.sequence);
  }

  function findMaxSectionNumber(): number {
    // Only consider blocks with section > 0 for max section calculation
    return blockList.value.reduce((max, block) => {
      if (block.section > 0) {
        return Math.max(max, block.section);
      }
      return max;
    }, 1);
  }

  // ===== เครื่องมือประเภทบล็อก =====
  function isAnswerItemBlockType(type: string): boolean {
    return ALLOWED_BLOCK_TYPES.includes(type);
  }

  function initializeBlocks(initialBlocks: ItemBlock[]) {
    blockList.value = [...initialBlocks];
  }

  function resetBlocks(initialBlocks: ItemBlock[]) {
    blockList.value = initialBlocks;
  }

  function getAssessmentData() {
    return {
      blocks: blockList.value,
      totalBlocks: blockList.value.length,
      totalSections: totalSections.value,
    };
  }

  // ===== เครื่องมือการลบบล็อก =====
  async function removeBlockFromList(targetIndex: number) {
    const blockToDelete = blockList.value[targetIndex];
    blockList.value.splice(targetIndex, 1);

    if (blockToDelete && blockToDelete.type === 'HEADER') {
      await handleHeaderBlockDeletion(blockToDelete);
    }
  }

  async function handleHeaderBlockDeletion(deletedHeaderBlock: ItemBlock) {
    const deletedSectionNumber = deletedHeaderBlock.section;

    const blocksInDeletedSection = blockList.value.filter(
      (block) => block.section === deletedSectionNumber,
    );

    blockList.value = blockList.value.filter((block) => block.section !== deletedSectionNumber);

    const { updatedBlocks, sectionMap } = updateSectionNumbersAfterDeletion(blockList.value);

    updateAssessmentAfterSectionDeletion(deletedSectionNumber, sectionMap);
    await performBlockDeletionBackendOperations(blocksInDeletedSection, updatedBlocks);
    await executeComplexUIRefresh();
  }

  function updateSectionNumbersAfterDeletion(remainingBlocks: ItemBlock[]) {
    // Only consider headers that represent actual sections (section > 0)
    const headerBlocks = remainingBlocks
      .filter((block) => block.type === 'HEADER' && block.section > 0)
      .sort((a, b) => a.section - b.section);

    const sectionMap = new Map<number, number>();
    headerBlocks.forEach((header, idx) => {
      const oldSection = header.section;
      const newSection = idx + 1;
      sectionMap.set(oldSection, newSection);
      header.section = newSection;
    });

    const blocksNeedingUpdate: ItemBlock[] = [];
    remainingBlocks.forEach((block) => {
      // Only update blocks that have actual sections (section > 0)
      if (block.section > 0 && sectionMap.has(block.section)) {
        const oldSection = block.section;
        const newSection = sectionMap.get(block.section)!;
        if (oldSection !== newSection) {
          block.section = newSection;
          blocksNeedingUpdate.push(block);
        }
      }
    });

    return {
      updatedBlocks: [...headerBlocks, ...blocksNeedingUpdate],
      sectionMap,
    };
  }

  function updateAssessmentAfterSectionDeletion(
    deletedSectionNumber: number,
    sectionMap: Map<number, number>,
  ) {
    if (activeAssessment.value && activeAssessment.value.itemBlocks) {
      activeAssessment.value.itemBlocks = activeAssessment.value.itemBlocks.filter((block) => {
        if (block.section === deletedSectionNumber) return false;
        if (sectionMap.has(block.section)) {
          block.section = sectionMap.get(block.section)!;
        }
        return true;
      });
    }
  }

  async function performBlockDeletionBackendOperations(
    blocksToDelete: ItemBlock[],
    blocksToUpdate: ItemBlock[],
  ) {
    const assessmentId = activeAssessment.value?.id;
    if (!assessmentId) return;

    const assessmentService = new AssessmentService('evaluate');

    try {
      if (blocksToDelete.length > 0) {
        await Promise.all(
          blocksToDelete.map(async (block) => {
            try {
              await assessmentService.deleteBlock(block);
            } catch (error) {
              console.error(`❌ Failed to delete block ${block.id}:`, error);
            }
          }),
        );
      }

      if (blocksToUpdate.length > 0) {
        await Promise.all(
          blocksToUpdate.map(async (block) => {
            try {
              await assessmentService.updateBlock({ ...block, assessmentId });
            } catch (error) {
              console.error(`❌ Failed to update block ${block.id}:`, error);
            }
          }),
        );
      }
    } catch (error) {
      console.error('❌ Error during backend operations:', error);
    }
  }

  // ===== เครื่องมือการสร้างบล็อก =====
  async function createNewBlockAfterIndex(
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null,
  ) {
    console.log(
      'assessmentId',
      assessmentId,
      'Creating new block at index:',
      targetIndex,
      'for assessment type:',
      assessmentType,
    );

    if (isBlockCreationInProgress.value) return;

    try {
      isBlockCreationInProgress.value = true;
      isFabCreationInProgress.value = true;

      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Creating new question...');

      const finalAssessmentId = assessmentId || getAssessmentId();
      console.log('Final assessment ID:', finalAssessmentId);

      // if (!finalAssessmentId || !validateAssessmentForOperation('block creation')) {
      //   console.log('Assessment ID not found', activeAssessment.value?.id);

      //   globalStore.completeSaveOperation(false, 'Assessment ID not found');
      //   return;
      // }

      if (!finalAssessmentId) {
        console.log('Assessment ID not found', activeAssessment.value?.id);

        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return;
      }

      // console.log('EIEIEIEIEEIEIEIEIEIEIEIEIEIEIEIEIEI');

      const globalIsRequired = activeAssessment.value?.globalIsRequired ?? false;
      const currentSection = getCurrentSection(blockList.value, targetIndex);

      // Get the current block's sequence and insert the new block right after it
      const currentBlock = blockList.value[targetIndex];
      const newSequence = currentBlock ? currentBlock.sequence + 1 : targetIndex + 1;

      const newBlockData = {
        assessmentId: finalAssessmentId,
        sequence: newSequence,
        section: currentSection,
        type: 'RADIO' as const,
        isRequired: globalIsRequired,
      };

      const assessmentService = new AssessmentService(assessmentType);

      console.log('activeAssessment:', activeAssessment.value);
      console.log('assessmentId:', activeAssessment.value?.id);
      const createdBlock = await assessmentService.createBlock(newBlockData);

      if (createdBlock) {
        await handleSuccessfulBlockCreation(createdBlock, targetIndex, assessmentType, globalStore);
      } else {
        globalStore.completeSaveOperation(false, 'Failed to create question');
      }
    } catch (error) {
      console.error('❌ Error creating new block:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error creating question');
    } finally {
      isBlockCreationInProgress.value = false;
      cleanupBlockCreationState();
    }
  }

  async function handleSuccessfulBlockCreation(
    createdBlock: ItemBlock,
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    globalStore: ReturnType<typeof useGlobalStore>,
  ) {
    const newBlockId = createdBlock.id;

    // Set FAB protection immediately
    fabTargetBlockId.value = newBlockId;
    isFabPositionLocked.value = true;
    currentSelectedBlockId.value = `block-${newBlockId}`;

    // Use the new sequence management function to properly handle insertion
    insertBlockWithSequenceManagement(createdBlock, targetIndex);

    // CRITICAL: Sync sequences with backend to ensure persistence
    await syncSequencesToBackend(assessmentType);

    // Update assessment if needed
    if (assessmentType === 'evaluate' && activeAssessment.value) {
      updateAssessmentWithNewBlock(createdBlock, targetIndex);
    }

    globalStore.completeSaveOperation(true, 'Question created successfully');

    // Position FAB and scroll
    await positionFabAndScrollToBlock(newBlockId);

    // Extended cleanup period
    setTimeout(() => {
      cleanupBlockCreationState();
      currentSelectedBlockId.value = `block-${newBlockId}`;
    }, 1000);
  }

  function updateAssessmentWithNewBlock(newBlock: ItemBlock, insertIndex: number) {
    if (!activeAssessment.value) return;

    const currentBlocks = activeAssessment.value.itemBlocks || [];

    // Find the current block to determine insertion point by sequence, not array index
    const currentBlock = blockList.value[insertIndex];
    if (!currentBlock) {
      // If no current block, just append at the end
      activeAssessment.value.itemBlocks = [...currentBlocks, newBlock];
      return;
    }

    // Insert the new block after the current block based on sequence
    const insertSequence = currentBlock.sequence + 1;

    // Update sequences of existing blocks that come after the insertion point
    currentBlocks.forEach((block) => {
      if (block.sequence >= insertSequence) {
        block.sequence = block.sequence + 1;
      }
    });

    // Insert the new block at the correct position
    const insertPosition = currentBlocks.findIndex((block) => block.sequence > newBlock.sequence);
    if (insertPosition === -1) {
      // Insert at the end
      activeAssessment.value.itemBlocks = [...currentBlocks, newBlock];
    } else {
      // Insert at the correct position
      const newAssessmentBlocks = [
        ...currentBlocks.slice(0, insertPosition),
        newBlock,
        ...currentBlocks.slice(insertPosition),
      ];
      activeAssessment.value.itemBlocks = newAssessmentBlocks;
    }
  }

  function cleanupBlockCreationState() {
    if (isFabCreationInProgress.value) {
      isFabCreationInProgress.value = false;
      fabTargetBlockId.value = null;
      isFabPositionLocked.value = false;
    }
  }

  // ===== การสร้างบล็อกส่วนหัว =====
  async function createNewHeaderAfterIndex(
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null,
    assignSection: boolean = true,
  ) {
    if (isBlockCreationInProgress.value) return;

    try {
      isBlockCreationInProgress.value = true;
      isFabCreationInProgress.value = true;

      const globalStore = useGlobalStore();
      const operationMessage = assignSection
        ? 'Creating new section header...'
        : 'Creating new text header...';
      globalStore.startSaveOperation(operationMessage);

      const finalAssessmentId = assessmentId || getAssessmentId();
      // if (!finalAssessmentId || !validateAssessmentForOperation('header creation')) {
      //   globalStore.completeSaveOperation(false, 'Assessment ID not found');
      //   return;
      // }

      if (!finalAssessmentId) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return;
      }

      // Determine section assignment based on the assignSection parameter
      // Use section 0 for text-only headers (not actual sections)
      const sectionToAssign = assignSection ? getCurrentSection(blockList.value, targetIndex) : 0;

      // Get the current block's sequence and insert the new block right after it
      const currentBlock = blockList.value[targetIndex];
      const newSequence = currentBlock ? currentBlock.sequence + 1 : targetIndex + 1;

      const newHeaderData = {
        assessmentId: finalAssessmentId,
        sequence: newSequence,
        section: sectionToAssign,
        type: 'HEADER' as const,
        isRequired: false,
      };

      const assessmentService = new AssessmentService(assessmentType);

      console.log('activeAssessment:', activeAssessment.value);
      console.log('assessmentId:', activeAssessment.value?.id);
      const createdHeader = await assessmentService.createBlock(newHeaderData);

      if (createdHeader) {
        await handleSuccessfulHeaderCreation(
          createdHeader,
          targetIndex,
          assessmentType,
          globalStore,
          assignSection,
        );
      } else {
        const errorMessage = assignSection
          ? 'Failed to create section header'
          : 'Failed to create text header';
        globalStore.completeSaveOperation(false, errorMessage);
      }
    } catch (error) {
      console.error('❌ Error creating header block:', error);
      const globalStore = useGlobalStore();
      const errorMessage = assignSection
        ? 'Error creating section header'
        : 'Error creating text header';
      globalStore.completeSaveOperation(false, errorMessage);
    } finally {
      isBlockCreationInProgress.value = false;
      cleanupBlockCreationState();
    }
  }

  async function handleSuccessfulHeaderCreation(
    createdHeader: ItemBlock,
    targetIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    globalStore: ReturnType<typeof useGlobalStore>,
    assignSection: boolean = true,
  ) {
    const headerId = createdHeader.id;

    // Set FAB protection
    fabTargetBlockId.value = headerId;
    isFabPositionLocked.value = true;
    currentSelectedBlockId.value = `block-${headerId}`;

    // Use the new sequence management function to properly handle insertion
    insertBlockWithSequenceManagement(createdHeader, targetIndex);

    // CRITICAL: Sync sequences with backend to ensure persistence
    await syncSequencesToBackend(assessmentType);

    // Update assessment if needed
    if (assessmentType === 'evaluate' && activeAssessment.value) {
      updateAssessmentWithNewBlock(createdHeader, targetIndex);
    }

    const successMessage = assignSection
      ? 'Section header created successfully'
      : 'Text header created successfully';
    globalStore.completeSaveOperation(true, successMessage);
    await positionFabAndScrollToBlock(headerId);

    setTimeout(() => {
      cleanupBlockCreationState();
      currentSelectedBlockId.value = `block-${headerId}`;
    }, 800);
  }

  // ===== การทำสำเนาบล็อก =====
  async function duplicateHeaderBlockAtomically(
    sourceBlockId: number,
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null,
  ) {
    if (isBlockCreationInProgress.value) return;

    const sourceBlockIndex = blockList.value.findIndex((block) => block.id === sourceBlockId);
    if (sourceBlockIndex === -1) {
      // ❌ ไม่พบ header block ต้นทางสำหรับการทำสำเนา
      console.error('❌ Source header block not found for duplication:', sourceBlockId);
      return;
    }

    const sourceBlock = blockList.value[sourceBlockIndex];
    if (!sourceBlock || sourceBlock.type !== 'HEADER') {
      // ❌ block ต้นทางไม่ใช่ header block
      console.error('❌ Source block is not a header block:', sourceBlock);
      return;
    }

    try {
      isBlockCreationInProgress.value = true;
      isFabCreationInProgress.value = true;

      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Duplicating header (atomic operation)...');

      const finalAssessmentId = assessmentId || getAssessmentId();
      if (!validateAssessmentForOperation('header duplication')) {
        globalStore.completeSaveOperation(false, 'Assessment ID not found');
        return;
      }

      const currentSection = getCurrentSection(blockList.value, sourceBlockIndex);
      const assessmentService = new AssessmentService(assessmentType);

      const duplicatedBlock = await assessmentService.duplicateBlock(sourceBlockId, {
        assessmentId: finalAssessmentId!,
        sequence: sourceBlock.sequence + 1,
        section: currentSection,
      });

      if (duplicatedBlock) {
        await handleSuccessfulBlockDuplication(
          duplicatedBlock,
          sourceBlockIndex,
          assessmentType,
          globalStore,
        );
      } else {
        globalStore.completeSaveOperation(false, 'Failed to duplicate header');
      }
    } catch (error) {
      console.error('❌ Error during header block duplication:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error duplicating header');
    } finally {
      isBlockCreationInProgress.value = false;
      cleanupBlockCreationState();
    }
  }

  async function handleSuccessfulBlockDuplication(
    duplicatedBlock: ItemBlock,
    sourceIndex: number,
    assessmentType: 'quiz' | 'evaluate',
    globalStore: ReturnType<typeof useGlobalStore>,
  ) {
    const duplicatedId = duplicatedBlock.id;

    // Use the new sequence management function to properly handle insertion
    insertBlockWithSequenceManagement(duplicatedBlock, sourceIndex);

    // CRITICAL: Sync sequences with backend to ensure persistence
    await syncSequencesToBackend(assessmentType);

    // Update assessment if needed
    if (assessmentType === 'evaluate' && activeAssessment.value) {
      updateAssessmentWithNewBlock(duplicatedBlock, sourceIndex);
    }

    // Set FAB protection
    fabTargetBlockId.value = duplicatedId;
    isFabPositionLocked.value = true;

    await positionFabAndScrollToBlock(duplicatedId);
    currentSelectedBlockId.value = `block-${duplicatedId}`;

    const hasContent =
      duplicatedBlock.headerBody &&
      (duplicatedBlock.headerBody.title || duplicatedBlock.headerBody.description);

    globalStore.completeSaveOperation(
      true,
      hasContent
        ? 'Header duplicated successfully with content (atomic)'
        : 'Header duplicated successfully (atomic)',
    );

    setTimeout(() => {
      cleanupBlockCreationState();
      currentSelectedBlockId.value = `block-${duplicatedId}`;
    }, 300);
  }

  // ===== การสร้างหมวดหมู่ใหม่ =====
  async function createNewSection(
    assessmentType: 'quiz' | 'evaluate',
    assessmentId?: number | null,
  ) {
    if (isBlockCreationInProgress.value) return;

    try {
      isBlockCreationInProgress.value = true;
      isFabCreationInProgress.value = true;

      const globalStore = useGlobalStore();
      globalStore.startSaveOperation('Creating new section...');

      const finalAssessmentId = assessmentId || getAssessmentId();

      // ✅ FIX: Use the same validation pattern as other methods - check finalAssessmentId directly
      // instead of relying on validateAssessmentForOperation which depends on activeAssessment
      if (!finalAssessmentId) {
        console.error('❌ [STORE] Assessment ID is required for section creation:', {
          passedAssessmentId: assessmentId,
          storeAssessmentId: getAssessmentId(),
          activeAssessmentId: activeAssessment.value?.id,
        });
        globalStore.completeSaveOperation(false, 'Assessment ID is required for section creation');
        return;
      }

      const newSectionNumber = findMaxSectionNumber() + 1;
      const newHeaderData = {
        assessmentId: finalAssessmentId,
        sequence: blockList.value.length + 1,
        section: newSectionNumber,
        type: 'HEADER' as const,
        isRequired: false,
      };

      const assessmentService = new AssessmentService(assessmentType);

      console.log('activeAssessment:', activeAssessment.value);
      console.log('assessmentId:', activeAssessment.value?.id);
      const createdHeader = await assessmentService.createBlock(newHeaderData);

      if (createdHeader) {
        await handleSuccessfulSectionCreation(createdHeader, assessmentType, globalStore);
      } else {
        globalStore.completeSaveOperation(false, 'Failed to create section');
      }
    } catch (error) {
      console.error('❌ Error creating section:', error);
      const globalStore = useGlobalStore();
      globalStore.completeSaveOperation(false, 'Error creating section');
    } finally {
      isBlockCreationInProgress.value = false;
      cleanupBlockCreationState();
    }
  }

  // Create NEXTSECTION blocks for sections that don't have them
  async function createNextSectionBlocks(
    createdHeader: ItemBlock,
    assessmentType: 'quiz' | 'evaluate',
  ) {
    try {
      const assessmentService = new AssessmentService(assessmentType);
      const finalAssessmentId = getAssessmentId();

      if (!finalAssessmentId) {
        console.error('❌ Assessment ID required for creating NEXTSECTION blocks');
        return;
      }

      // Group blocks by section
      const sectionGroups = new Map<number, ItemBlock[]>();
      blockList.value.forEach((block) => {
        if (!sectionGroups.has(block.section)) {
          sectionGroups.set(block.section, []);
        }
        sectionGroups.get(block.section)!.push(block);
      });

      // For each section (except the last one), check if it has a NEXTSECTION block
      const sortedSections = Array.from(sectionGroups.keys()).sort((a, b) => a - b);

      for (let i = 0; i < sortedSections.length - 1; i++) {
        const sectionNumber = sortedSections[i];
        if (sectionNumber === undefined) continue;
        const sectionBlocks = sectionGroups.get(sectionNumber) || [];

        // Check if this section already has a NEXTSECTION block
        const hasNextSectionBlock = sectionBlocks.some((block) => block.type === 'NEXTSECTION');

        if (!hasNextSectionBlock) {
          // Find the last block in this section to determine sequence
          const lastBlockInSection = sectionBlocks
            .filter((block) => block.sequence)
            .sort((a, b) => b.sequence - a.sequence)[0];

          const nextSequence = lastBlockInSection
            ? lastBlockInSection.sequence + 1
            : sectionBlocks.length + 1;

          // Create NEXTSECTION block for this section
          const nextSectionData = {
            assessmentId: finalAssessmentId,
            sequence: nextSequence,
            section: sectionNumber,
            type: 'NEXTSECTION' as const,
            isRequired: false,
          };

          console.log('🔄 Creating NEXTSECTION block for section:', sectionNumber);
          const createdNextSection = await assessmentService.createBlock(nextSectionData);

          if (createdNextSection) {
            // Insert the NEXTSECTION block at the correct position within the section
            // Find the index where this block should be inserted
            const insertIndex = blockList.value.findIndex(
              (block) =>
                block.section > sectionNumber ||
                (block.section === sectionNumber && block.sequence > nextSequence),
            );

            if (insertIndex === -1) {
              // If no block found, add to end
              addBlockToEnd(createdNextSection);
            } else {
              // Insert at the correct position
              blockList.value.splice(insertIndex, 0, createdNextSection);
            }

            console.log('✅ NEXTSECTION block created for section:', sectionNumber);
          }
        }
      }
    } catch (error) {
      console.error('❌ Error creating NEXTSECTION blocks:', error);
    }
  }

  async function handleSuccessfulSectionCreation(
    createdHeader: ItemBlock,
    assessmentType: 'quiz' | 'evaluate',
    globalStore: ReturnType<typeof useGlobalStore>,
  ) {
    const headerId = createdHeader.id;

    // Add to end of list
    addBlockToEnd(createdHeader);

    // Create NEXTSECTION blocks for existing sections and the new section
    await createNextSectionBlocks(createdHeader, assessmentType);

    // CRITICAL: Sync sequences with backend to ensure persistence
    await syncSequencesToBackend(assessmentType);

    // Update assessment if needed
    if (assessmentType === 'evaluate' && activeAssessment.value) {
      const currentBlocks = activeAssessment.value.itemBlocks || [];
      const newAssessmentBlocks = [...currentBlocks, createdHeader];
      newAssessmentBlocks.forEach((block, idx) => {
        block.sequence = idx + 1;
      });
      activeAssessment.value.itemBlocks = newAssessmentBlocks;
    }

    await positionFabAndScrollToBlock(headerId);
    currentSelectedBlockId.value = `block-${headerId}`;
    globalStore.completeSaveOperation(true, 'Section created successfully');

    setTimeout(() => {
      cleanupBlockCreationState();
      currentSelectedBlockId.value = `block-${headerId}`;
    }, 800);
  }

  // ===== การจัดการการประเมินผล =====
  // async function fetchAssessmentById(id: number) {
  //   isLoadingAssessment.value = true;
  //   assessmentError.value = null;
  //   try {
  //     const response = await new AssessmentService('evaluate').fetchOne(id);
  //     if (response) {
  //       activeAssessment.value = response;
  //       if (response.itemBlocks) {
  //         blockList.value = response.itemBlocks;
  //       }
  //     }
  //   } catch (err: unknown) {
  //     assessmentError.value = err instanceof Error ? err.message : 'ไม่สามารถโหลดแบบทดสอบได้';
  //   } finally {
  //     isLoadingAssessment.value = false;
  //   }
  // }

  async function fetchAssessmentById(id: number) {
    isLoadingAssessment.value = true;
    assessmentError.value = null;
    try {
      const { assessment, pagedItemBlocks } = await new AssessmentService('evaluate').fetchOne(id);
      if (assessment) {
        activeAssessment.value = assessment;
        if (pagedItemBlocks?.length) {
          blockList.value = pagedItemBlocks;
        }
      }
    } catch (err: unknown) {
      assessmentError.value = err instanceof Error ? err.message : 'ไม่สามารถโหลดแบบทดสอบได้';
    } finally {
      isLoadingAssessment.value = false;
    }
  }

  async function addAssessment(assessmentData: Partial<Assessment>): Promise<Assessment> {
    const response = await new AssessmentService('evaluate').createOne(assessmentData);
    assessmentCollection.value.push(response);
    activeAssessment.value = response;
    return response;
  }

  async function updateAssessment(id: number, assessmentData: Assessment): Promise<Assessment> {
    const response = await new AssessmentService('evaluate').updateOne(id, assessmentData);
    const index = assessmentCollection.value.findIndex((assessment) => assessment.id === id);
    if (index !== -1) assessmentCollection.value[index] = response;
    if (activeAssessment.value?.id === id) activeAssessment.value = response;
    return response;
  }

  async function removeAssessment(id: number): Promise<void> {
    await new AssessmentService('evaluate').deleteOne(id);
    assessmentCollection.value = assessmentCollection.value.filter(
      (assessment) => assessment.id !== id,
    );
    if (activeAssessment.value?.id === id) {
      activeAssessment.value = null;
    }
  }

  // ===== ฟังก์ชันการตรวจสอบ =====
  function validateAssessmentIds(): { valid: boolean; missing: string[] } {
    const missing: string[] = [];

    if (!activeAssessment.value?.id) {
      missing.push('assessmentId');
    }

    if (!activeAssessment.value?.itemBlocks || activeAssessment.value.itemBlocks.length === 0) {
      missing.push('itemBlocks');
    } else {
      activeAssessment.value.itemBlocks.forEach((block, index) => {
        if (!block.id) {
          missing.push(`itemBlock[${index}].id`);
        }
        if (!block.assessmentId) {
          missing.push(`itemBlock[${index}].assessmentId`);
        }
      });
    }

    return {
      valid: missing.length === 0,
      missing,
    };
  }

  function validateBlockDeletion(blockId: number): { canDelete: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!activeAssessment.value) {
      issues.push('No current assessment loaded');
      return { canDelete: false, issues };
    }

    if (!blockId) {
      issues.push('Invalid block ID provided');
      return { canDelete: false, issues };
    }

    const targetBlock = activeAssessment.value.itemBlocks?.find((block) => block.id === blockId);
    if (!targetBlock) {
      issues.push(`Block with ID ${blockId} not found in current assessment`);
      return { canDelete: false, issues };
    }

    // ✅ FIX: Remove strict headerBody validation - allow deletion even without headerBody
    // Header blocks can be deleted even if they don't have headerBody data
    if (targetBlock.type === 'HEADER' && !targetBlock.headerBody) {
      // This is just a warning, not a blocking issue
      console.warn('⚠️ Header block missing headerBody data, but deletion will proceed');
    }

    if (targetBlock.assessmentId !== activeAssessment.value.id) {
      issues.push(
        `Block assessmentId (${targetBlock.assessmentId}) does not match current assessment ID (${activeAssessment.value.id})`,
      );
    }

    return {
      canDelete: issues.length === 0,
      issues,
    };
  }

  function validatePostDeletion(deletedBlockId: number): { success: boolean; issues: string[] } {
    const issues: string[] = [];

    if (!activeAssessment.value) {
      issues.push('No current assessment loaded');
      return { success: false, issues };
    }

    const blockStillExists = activeAssessment.value.itemBlocks?.some(
      (block) => block.id === deletedBlockId,
    );
    if (blockStillExists) {
      issues.push(`Block with ID ${deletedBlockId} still exists in assessment after deletion`);
    }

    const orphanedQuestions = activeAssessment.value.itemBlocks?.some((block) =>
      block.questions?.some((question) => question.itemBlockId === deletedBlockId),
    );
    if (orphanedQuestions) {
      issues.push(`Found orphaned questions referencing deleted block ID ${deletedBlockId}`);
    }

    const orphanedOptions = activeAssessment.value.itemBlocks?.some((block) =>
      block.options?.some((option) => option.itemBlockId === deletedBlockId),
    );
    if (orphanedOptions) {
      issues.push(`Found orphaned options referencing deleted block ID ${deletedBlockId}`);
    }

    return {
      success: issues.length === 0,
      issues,
    };
  }

  function resetStore() {
    blockList.value = [];
    currentSelectedBlock.value = null;
    currentSelectedBlockId.value = undefined;
    assessmentMetadata.value = null;
    assessmentCollection.value = [];
    activeAssessment.value = null;
    isLoadingAssessment.value = false;
    assessmentError.value = null;
    paginationPage.value = 1;
    paginationLimit.value = 5;
    searchQuery.value = '';
    showDuplicateDialog.value = false;
    isBlockCreationInProgress.value = false;
    isDragOperationActive.value = false;
    uiRefreshTrigger.value = 0;
    itemBlockPage.value = 1;
    hasMoreBlocks.value = true;
    fabPositioningState.value = {
      isPositionLocked: false,
      pendingTargetPosition: null,
      isCreationInProgress: false,
      targetBlockId: null,
    };
    questionIdCounter.value = 0;
    optionIdCounter.value = 0;
  }

  // ===== STORE API EXPORT =====
  return {
    // ===== BLOCK MANAGEMENT STATE (with backward compatibility) =====
    blocks: blockList,
    selectedBlock: currentSelectedBlock,
    selectedBlockId: currentSelectedBlockId,

    // ===== ASSESSMENT MANAGEMENT STATE (with backward compatibility) =====
    assessments: assessmentCollection,
    currentAssessment: activeAssessment,
    loading: isLoadingAssessment,
    error: assessmentError,
    meta: assessmentMetadata,
    page: paginationPage,
    limit: paginationLimit,
    search: searchQuery,

    // ===== UI INTERACTION STATE (with backward compatibility) =====
    duplicateDialog: showDuplicateDialog,
    isCreatingBlock: isBlockCreationInProgress,
    isDragging: isDragOperationActive,
    forceUpdateTrigger: uiRefreshTrigger,

    // ===== FAB STATE (with backward compatibility) =====
    fabState: fabPositioningState,
    fabPositionLock: isFabPositionLocked,
    pendingFabPosition: pendingFabTargetPosition,
    blockCreationInProgress: isFabCreationInProgress,
    targetBlockId: fabTargetBlockId,

    // ===== TIMEOUT MANAGEMENT =====
    timeoutManager: fabTimeoutManager,

    // ===== BLOCK OPERATIONS (with backward compatibility) =====
    addBlock: insertBlockAfterIndex,
    appendBlock: addBlockToEnd,
    updateBlocksOrder: reorderBlocksAndUpdateSequences,
    setSection: updateBlockSection,
    updateBlock: replaceBlockAtIndex,
    deleteBlock: removeBlockFromList,
    duplicateBlock: createDuplicateBlock,
    insertBlockWithSequenceManagement,
    syncSequencesToBackend,

    // ===== BLOCK CREATION METHODS (with backward compatibility) =====
    handleAddBlockAfter: createNewBlockAfterIndex,
    handleAddHeaderAfter: createNewHeaderAfterIndex,
    handleDuplicateHeaderBlock: duplicateHeaderBlockAtomically,
    handleAddSection: createNewSection,

    // ===== FAB AND SCROLL MANAGEMENT (with backward compatibility) =====
    setFabPosition: updateFabPosition,
    createFabProtection: createFabProtectionForBlock,
    scrollToTarget: scrollToSelectedBlock,
    setFabAndScroll: positionFabAndScrollToBlock,

    // ===== UI STATE MANAGEMENT (with backward compatibility) =====
    forceRefreshBlocks: executeBasicUIRefresh,
    syncBlocksWithAssessment: synchronizeBlocksWithAssessment,

    // ===== BLOCK REFERENCE MANAGEMENT (with backward compatibility) =====
    // ===== การจัดการ Reference ของบล็อก (เพื่อความเข้ากันได้ย้อนหลัง) =====
    setBlockRef: setBlockDOMReference,
    getBlockRef: getBlockDOMReference,

    // ===== ยูทิลิตี้เกี่ยวกับบล็อก =====
    isAnswerItemBlockType,
    isSectionBlock,
    isSectionBlockById,
    totalSections,
    getSectionNumber,
    getSectionNumberBySequence,
    getSectionNumberById,
    resetBlocks,
    initializeBlocks,
    getAssessmentData,

    // ===== การสร้างรหัส (เพื่อความเข้ากันได้ย้อนหลัง) =====
    generateQuestionId: generateNextQuestionId,
    generateOptionId: generateNextOptionId,

    // ===== การจัดการแบบประเมิน =====
    fetchAssessmentById,
    addAssessment,
    updateAssessment,
    removeAssessment,

    // ===== ID TRACKING HELPERS =====
    getAssessmentId,
    getItemBlockById,
    getHeaderBlockId,
    getRadioBlockId,
    getAllItemBlockIds,

    // ===== VALIDATION HELPERS =====
    validateIds: validateAssessmentIds,
    validateBlockDeletion,
    validatePostDeletion,

    // ===== PAGINATION ITEMBLOCK =====
    updateOptionTextInStore,
    itemBlockPage,
    hasMoreBlocks,
    loadMoreBlocks,
    resetStore,
    addBlocks,
  };
});
