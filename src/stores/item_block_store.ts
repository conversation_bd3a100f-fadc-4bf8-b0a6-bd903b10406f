import { defineStore } from 'pinia';
import type { ItemBlock, Option, Question, ItemBlockType } from 'src/types/models';
import { ref } from 'vue';

// กำหนดประเภท store สำหรับ TypeScript
export interface ItemBlockStore {
  // สถานะ
  radioOptions: {
    id?: number;
    placeholder: string;
    value: string;
    optionText: string;
    score: number;
    sequence: number;
  }[];
  optionSelectedOption: string[];
  checkboxOptions: {
    id?: number;
    placeholder: string;
    value: string;
    optionText: string;
    score: number;
    sequence: number;
  }[];
  checkboxSelectedOptions: string[];
  gridRowQuestions: { id?: number; label: string; value: string; sequence: number }[];
  // ตัวเลือกคอลัมน์ที่ใช้ร่วมกันสำหรับ grid row ทั้งหมด
  gridColumnOptions: {
    id?: number;
    label: string;
    value: string;
    optionText: string;
    score: number;
    sequence: number;
  }[];
  textInput: string;
  draggedIndex: number | null;
  draggedSection: 'row' | 'col' | null;
  hoveredIndex: number | null;

  // ฟังก์ชันการกระทำ
  updateOption: (index: number, isCheckbox?: boolean) => void;
  addOption: (isCheckbox?: boolean) => void;
  addOtherOption: (isCheckbox?: boolean) => void;
  removeOption: (index: number, isCheckbox?: boolean) => void;
  addRowQuestion: () => void;
  addColumnOption: () => void;
  removeRowQuestion: (index: number) => void;
  removeColumnOption: (index: number) => void;
  updateRowQuestion: (index: number) => void;
  updateColumnOption: (index: number) => void;
  startDrag: (index: number, section?: 'row' | 'col') => void;
  handleDragStart: (event: DragEvent) => void;
  hoverRow: (index: number) => void;
  drop: (index: number, event: DragEvent, isCheckbox?: boolean) => void;
  gridDrop: (index: number, event: DragEvent, section: 'row' | 'col') => void;
  endDrag: () => void;

  // ฟังก์ชัน extract
  extractOptions: () => Option[];
  extractQuestions: () => Question[];
  extractItemBlockData: () => {
    options: Option[];
    questions: Question[];
    type?: ItemBlockType;
    sequence?: number;
    section?: number;
    isRequired: boolean;
  };

  // ฟังก์ชันช่วยสำหรับจัดการ ID
  findOptionById: (
    optionId: number,
    isCheckbox?: boolean,
  ) => {
    option: {
      id?: number;
      placeholder: string;
      value: string;
      optionText: string;
      score: number;
      sequence: number;
    } | null;
    index: number;
  };
  updateOptionId: (index: number, optionId: number, isCheckbox?: boolean) => void;
  syncOptionSequences: (isCheckbox?: boolean) => Promise<void>;
  syncGridColumnSequences: () => Promise<void>;
}

export const createItemBlockStore = (id: number, itemBlock?: ItemBlock) =>
  defineStore(`item-block-${id}`, () => {
    // กำหนด type สำหรับ options
    type OptionType = {
      id?: number; // เพิ่ม ID สำหรับ sync กับ backend
      placeholder: string;
      value: string;
      optionText: string;
      score: number; // เพิ่ม property score
      sequence: number; // เพิ่ม property sequence
    };

    // กำหนด type สำหรับ grid row questions
    type GridRowQuestion = {
      id?: number; // เพิ่ม ID สำหรับ sync กับ backend
      label: string;
      value: string;
      sequence: number; // เพิ่ม property sequence
    };

    // กำหนด type สำหรับ grid column options
    type GridColumnOption = {
      id?: number; // เพิ่ม ID สำหรับ sync กับ backend
      label: string;
      value: string;
      optionText: string;
      score: number; // เพิ่ม property score
      sequence: number; // เพิ่ม property sequence
    };

    // สถานะ
    // ตัวเลือก
    const radioOptions = ref<OptionType[]>([
      { placeholder: '', value: '', optionText: '', score: 0, sequence: 1 },
    ]);

    // เพิ่มตัวเลือก "อื่นๆ" เฉพาะกรณี radio type
    // if (itemBlock?.type === 'RADIO') {
    //   radioOptions.value.push({
    //     placeholder: 'อื่นๆ',
    //     value: '',
    //     optionText: '',
    //     score: 0,
    //     sequence: 2,
    //   });
    // }

    const optionSelectedOption = ref<string[]>([]);

    // checkbox
    const checkboxOptions = ref<OptionType[]>([
      { placeholder: '', value: '', optionText: '', score: 0, sequence: 1 },
    ]);
    const checkboxSelectedOptions = ref<string[]>([]);

    // grid
    const gridRowQuestions = ref<GridRowQuestion[]>([
      { label: '', value: 'question1', sequence: 1 },
    ]);
    const gridColumnOptions = ref<GridColumnOption[]>([
      { label: '', value: 'option1', optionText: '', score: 0, sequence: 1 },
    ]);

    // text
    const textInput = ref('');
    const draggedIndex = ref<number | null>(null);
    const draggedSection = ref<'row' | 'col' | null>(null);
    const hoveredIndex = ref<number | null>(null);

    // กำหนดค่าเริ่มต้นจาก itemBlock ถ้ามีข้อมูล
    if (itemBlock) {
      // กำหนดค่า radio/checkbox options
      if (itemBlock.options && itemBlock.options.length > 0) {
        // เรียง options ตาม sequence ก่อน map เพื่อให้ลำดับถูกต้อง
        const sortedOptions = [...itemBlock.options].sort((a, b) => a.sequence - b.sequence);

        const mappedOptions = sortedOptions.map((opt) => ({
          id: opt.id, // ใส่ ID จาก backend สำหรับ sync
          placeholder: opt.optionText || `ตัวเลือกที่ ${opt.sequence}`,
          value: String(opt.value || `option${opt.sequence}`),
          optionText: opt.optionText || '',
          score: opt.value || 0, // ใช้ค่าจริงจาก backend
          sequence: opt.sequence,
        }));

        if (itemBlock.type === 'RADIO') {
          radioOptions.value = mappedOptions;
        } else if (itemBlock.type === 'CHECKBOX') {
          checkboxOptions.value = mappedOptions;
        } else if (itemBlock.type === 'GRID') {
          // เรียง grid column options ตาม sequence เช่นกัน
          const sortedGridOptions = [...itemBlock.options].sort((a, b) => a.sequence - b.sequence);

          gridColumnOptions.value = sortedGridOptions.map((option, idx) => ({
            id: option.id, // ใส่ ID จาก backend สำหรับ sync
            label: option.optionText || `ตัวเลือกที่ ${option.sequence}`,
            value: `option${idx + 1}`,
            optionText: option.optionText || '',
            score: option.value || 0,
            sequence: option.sequence,
          }));
        } else if (itemBlock.type === 'NEXTSECTION') {
          // For NEXTSECTION type, we don't need to map options to store format
          // The NextSectionItemBlock component will handle the options directly
          console.log('NEXTSECTION block initialized with options:', itemBlock.options);
        }
      }

      // กำหนดค่า grid row questions ถ้าเป็น grid type
      if (itemBlock.type === 'GRID' && itemBlock.questions && itemBlock.questions.length > 0) {
        // ข้าม header question (isHeader === true) และเรียงตาม sequence
        const rowQuestions = itemBlock.questions
          .filter((q) => !q.isHeader)
          .sort((a, b) => a.sequence - b.sequence);

        if (rowQuestions.length > 0) {
          gridRowQuestions.value = rowQuestions.map((q, idx) => ({
            id: q.id, // ใส่ ID จาก backend สำหรับ sync
            label: q.questionText || '',
            value: `question${idx + 1}`,
            sequence: q.sequence,
          }));
        }
      }

      // กำหนดค่า text input ถ้าเป็น textfield
      if (itemBlock.type === 'TEXTFIELD' && itemBlock.questions && itemBlock.questions.length > 0) {
        textInput.value = itemBlock.questions[0]?.questionText || '';
      }
    }

    // Actions
    // ฟังก์ชันการกระทำ
    const addOption = (isCheckbox: boolean = false) => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      const newIndex = options.length + 1;
      options.push({
        placeholder: '',
        value: ``,
        optionText: ``, // ว่างโดยค่าเริ่มต้น แสดง placeholder เท่านั้น
        score: 0,
        sequence: newIndex, // เพิ่ม property sequence
      });
    };

    const addOtherOption = (isCheckbox: boolean = false) => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      const newIndex = options.length + 1;
      options.push({
        placeholder: 'อื่นๆ',
        value: ``,
        optionText: '', // ว่างโดยค่าเริ่มต้น แสดง placeholder เท่านั้น
        score: 0,
        sequence: newIndex, // เพิ่ม property sequence
      });
    };

    const updateOption = (index: number, isCheckbox: boolean = false) => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      options[index]!.value =
        options[index]!.placeholder === 'อื่นๆ' ? `other${index + 1}` : `option${index + 1}`;

      // ไม่ตั้งค่า optionText เป็น placeholder อัตโนมัติ
      // ให้ผู้ใช้กรอกข้อความเอง

      // ตรวจสอบให้ score เป็นตัวเลข (ไม่รีเซ็ตเป็น 0)
      const score = options[index]!.score;
      if (typeof score !== 'number') {
        options[index]!.score = Number(score) || 0;
      }
    };

    const removeOption = (index: number, isCheckbox: boolean = false) => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      const selectedOptionsRef = isCheckbox ? checkboxSelectedOptions : optionSelectedOption;

      if (options.length > 1) {
        // ลบ option ที่ index ที่ระบุ
        // คงค่า ID และ property อื่น ๆ ของ option อื่นไว้
        options.splice(index, 1);

        // อัปเดตลำดับ sequence ของ option ที่เหลือให้ถูกต้อง
        options.forEach((option, idx) => {
          option.sequence = idx + 1;
          // อัปเดต value ให้สอดคล้อง (แต่คง ID และ property อื่นไว้)
          option.value = option.placeholder === 'อื่นๆ' ? `other${idx + 1}` : `option${idx + 1}`;
        });

        // อัปเดต selected options ให้ลบค่าที่ถูกลบออก
        selectedOptionsRef.value = selectedOptionsRef.value.filter((val) =>
          options.some((option) => option.value === val),
        );
      }
    };

    //grid
    const addRowQuestion = () => {
      const newIndex = gridRowQuestions.value.length + 1;

      gridRowQuestions.value.push({
        label: ``, // Empty by default, only show placeholder
        value: `question${newIndex}`,
        sequence: newIndex, // Add sequence property
      });
    };

    const addColumnOption = () => {
      const newIndex = gridColumnOptions.value.length + 1;
      gridColumnOptions.value.push({
        label: '',
        value: `option${newIndex}`,
        optionText: ``, // Empty by default, only show placeholder
        score: 0, // Default score value
        sequence: newIndex, // Add sequence property
      });
    };

    const removeRowQuestion = (index: number) => {
      if (gridRowQuestions.value.length > 1) {
        // Remove the question
        gridRowQuestions.value.splice(index, 1);

        // Renumber questions but preserve user-entered labels
        gridRowQuestions.value = gridRowQuestions.value.map((question, idx) => ({
          ...question,
          value: `question${idx + 1}`,
          // Preserve the original label entered by the user
          sequence: idx + 1,
        }));
      }
    };

    const removeColumnOption = (index: number) => {
      if (gridColumnOptions.value.length > 1) {
        gridColumnOptions.value.splice(index, 1);
        gridColumnOptions.value = gridColumnOptions.value.map((option, idx) => ({
          ...option,
          value: `option${idx + 1}`,
          // Preserve user-entered optionText
          // Preserve user-entered score
          sequence: idx + 1,
        }));
      }
    };

    const updateRowQuestion = (index: number) => {
      gridRowQuestions.value[index]!.value = `question${index + 1}`;

      // Don't automatically set label text
      // Let the user enter their own text
    };

    const updateColumnOption = (index: number) => {
      gridColumnOptions.value[index]!.value = `option${index + 1}`;

      // Don't automatically set optionText to label
      // Let the user enter their own text

      // Ensure score is a number (don't reset to 0)
      const score = gridColumnOptions.value[index]!.score;
      if (typeof score !== 'number') {
        gridColumnOptions.value[index]!.score = Number(score) || 0;
      }
    };

    // ฟังก์ชัน drag
    const startDrag = (index: number, section?: 'row' | 'col') => {
      draggedIndex.value = index;
      if (section) draggedSection.value = section;
    };

    const handleDragStart = (event: DragEvent) => {
      if (event.dataTransfer) {
        event.dataTransfer.effectAllowed = 'move';
      }
    };

    const hoverRow = (index: number) => {
      hoveredIndex.value = index;
    };

    const drop = (index: number, event: DragEvent, isCheckbox: boolean = false) => {
      event.preventDefault();
      if (draggedIndex.value === null || draggedIndex.value === index) return;

      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      const originalDraggedIndex = draggedIndex.value;

      // สร้างสำเนาของ option ที่ถูกลากพร้อม property ทั้งหมดรวมถึง ID
      const draggedOption: OptionType = {
        ...(options[originalDraggedIndex]!.id && { id: options[originalDraggedIndex]!.id }), // คงค่า ID ถ้ามี
        placeholder: options[originalDraggedIndex]!.placeholder,
        value: options[originalDraggedIndex]!.value,
        optionText: options[originalDraggedIndex]!.optionText,
        score: options[originalDraggedIndex]!.score,
        sequence: options[originalDraggedIndex]!.sequence,
      };

      // ทำการสลับลำดับ
      options.splice(originalDraggedIndex, 1);
      options.splice(index, 0, draggedOption);

      // อัปเดตค่าทั้งหมดใหม่หลัง drag
      options.forEach((option, idx) => {
        // อัปเดต value ตาม placeholder
        option.value = option.placeholder === 'อื่นๆ' ? `other${idx + 1}` : `option${idx + 1}`;

        // ตรวจสอบให้ score เป็นตัวเลข
        if (typeof option.score !== 'number') {
          option.score = Number(option.score) || 0;
        }

        // อัปเดต sequence ให้ตรงกับตำแหน่งใหม่
        option.sequence = idx + 1;
      });

      // รีเซ็ตสถานะ drag
      draggedIndex.value = null;
      hoveredIndex.value = null;

      // sync กับ backend (ไม่ await เพื่อไม่ block UI)
      syncOptionSequences(isCheckbox).catch((error) => {
        console.error('❌ [STORE] Failed to sync sequences after drag-and-drop:', error);
      });
    };

    const gridDrop = (index: number, event: DragEvent, section: 'row' | 'col') => {
      event.preventDefault();
      if (
        draggedIndex.value === null ||
        draggedSection.value !== section ||
        draggedIndex.value === index
      )
        return;

      const originalDraggedIndex = draggedIndex.value;

      if (section === 'row') {
        const sourceList = gridRowQuestions.value;
        const draggedItem = {
          label: sourceList[originalDraggedIndex]!.label,
          value: sourceList[originalDraggedIndex]!.value,
          sequence: sourceList[originalDraggedIndex]!.sequence,
          // คง property อื่น ๆ
          ...sourceList[originalDraggedIndex],
        };
        sourceList.splice(originalDraggedIndex, 1);
        gridRowQuestions.value.splice(index, 0, draggedItem);
      } else {
        const sourceList = gridColumnOptions.value;
        const draggedItem = {
          ...(sourceList[originalDraggedIndex]!.id && { id: sourceList[originalDraggedIndex]!.id }), // คง ID ถ้ามี
          label: sourceList[originalDraggedIndex]!.label,
          value: sourceList[originalDraggedIndex]!.value,
          optionText: sourceList[originalDraggedIndex]!.optionText,
          score: sourceList[originalDraggedIndex]!.score,
          sequence: sourceList[originalDraggedIndex]!.sequence,
          // คง property อื่น ๆ
          ...sourceList[originalDraggedIndex],
        };
        sourceList.splice(originalDraggedIndex, 1);
        gridColumnOptions.value.splice(index, 0, draggedItem);
      }

      if (section === 'row') {
        gridRowQuestions.value = gridRowQuestions.value.map((question, idx) => ({
          ...question,
          value: `question${idx + 1}`,
          // คง label เดิมที่ผู้ใช้กรอก
          // อัปเดต sequence ให้ตรงกับตำแหน่งใหม่
          sequence: idx + 1,
        }));
      } else {
        gridColumnOptions.value = gridColumnOptions.value.map((option, idx) => ({
          ...option,
          value: `option${idx + 1}`,
          // คง optionText เดิม
          // คงค่า score เดิม
          score: typeof option.score === 'number' ? option.score : Number(option.score) || 0,
          // อัปเดต sequence ให้ตรงกับตำแหน่งใหม่
          sequence: idx + 1,
        }));
      }

      draggedIndex.value = null;
      draggedSection.value = null;
      hoveredIndex.value = null;

      // sync กับ backend เฉพาะ column (ไม่ await เพื่อไม่ block UI)
      if (section === 'col') {
        syncGridColumnSequences().catch((error) => {
          console.error(
            '❌ [STORE] Failed to sync grid column sequences after drag-and-drop:',
            error,
          );
        });
      }
    };

    const endDrag = () => {
      draggedIndex.value = null;
      draggedSection.value = null;
      hoveredIndex.value = null;
    };

    // ฟังก์ชัน extract options และ questions สำหรับ API
    const extractOptions = (): Option[] => {
      if (itemBlock?.type === 'RADIO') {
        return radioOptions.value.map((option, index) => ({
          id: 0, // ให้ backend กำหนด
          itemBlockId: id,
          optionText: option.optionText || option.placeholder,
          value: option.score || 0,
          sequence: option.sequence || index + 1,
        }));
      } else if (itemBlock?.type === 'CHECKBOX') {
        return checkboxOptions.value.map((option, index) => ({
          id: 0, // ให้ backend กำหนด
          itemBlockId: id,
          optionText: option.optionText || option.placeholder,
          value: option.score || 0,
          sequence: option.sequence || index + 1,
        }));
      } else if (itemBlock?.type === 'GRID') {
        return gridColumnOptions.value.map((option, index) => ({
          id: option.id || 0, // ใช้ ID เดิมถ้ามี ไม่งั้นให้ backend กำหนด
          itemBlockId: id,
          optionText: option.optionText || option.label,
          value: option.score || 0,
          sequence: option.sequence || index + 1,
        }));
      } else if (itemBlock?.type === 'NEXTSECTION') {
        // For NEXTSECTION, return existing options from itemBlock
        // The NextSectionItemBlock component handles updates directly via API
        return itemBlock?.options || [];
      }
      return [];
    };

    const extractQuestions = (): Question[] => {
      const questions: Question[] = [];

      if (itemBlock?.type === 'TEXTFIELD') {
        questions.push({
          id: 0, // ให้ backend กำหนด
          itemBlockId: id,
          questionText: textInput.value || '',
          isHeader: false,
          sequence: 1,
          score: 0,
        });
      } else if (itemBlock?.type === 'GRID') {
        // เพิ่ม header question ก่อนถ้ามี
        const headerQuestion = itemBlock?.questions?.find((q) => q.isHeader);
        if (headerQuestion) {
          questions.push({
            ...headerQuestion,
            itemBlockId: id,
          });
        }

        // เพิ่ม row questions ที่ isHeader: false (จำเป็นสำหรับ GRID)
        gridRowQuestions.value.forEach((rowQuestion, index) => {
          questions.push({
            id: rowQuestion.id || 0, // ใช้ ID เดิมถ้ามี ไม่งั้นให้ backend กำหนด
            itemBlockId: id,
            questionText: rowQuestion.label || `Question ${index + 1}`,
            isHeader: false, // จำเป็น: GRID ทุกอันต้อง isHeader: false
            sequence: rowQuestion.sequence || index + (headerQuestion ? 2 : 1),
            score: 0,
          });
        });
      } else if (itemBlock?.type === 'UPLOAD') {
        // กรณี file type, extract questions ที่มีอยู่
        const existingQuestions = itemBlock?.questions || [];
        existingQuestions.forEach((question) => {
          questions.push({
            ...question,
            itemBlockId: id,
          });
        });
      }

      return questions;
    };

    const extractItemBlockData = () => {
      return {
        options: extractOptions(),
        questions: extractQuestions(),
        type: itemBlock?.type,
        sequence: itemBlock?.sequence,
        section: itemBlock?.section,
        isRequired: itemBlock?.isRequired || false,
      };
    };

    // ฟังก์ชันช่วยค้นหา option ตาม ID ใน store
    const findOptionById = (
      optionId: number,
      isCheckbox: boolean = false,
    ): { option: OptionType | null; index: number } => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      const index = options.findIndex((option) => option.id === optionId);
      return {
        option: index >= 0 ? options[index] || null : null,
        index: index >= 0 ? index : -1,
      };
    };

    // ฟังก์ชันช่วยอัปเดต option ID ใน store หลังสร้าง
    const updateOptionId = (index: number, optionId: number, isCheckbox: boolean = false) => {
      const options = isCheckbox ? checkboxOptions.value : radioOptions.value;
      if (options[index]) {
        options[index].id = optionId;
      }
    };

    // ฟังก์ชันช่วย sync ลำดับ option กับ backend หลัง drag-and-drop
    const syncOptionSequences = async (isCheckbox: boolean = false) => {
      try {
        const options = isCheckbox ? checkboxOptions.value : radioOptions.value;

        // sync เฉพาะ options ที่มี ID (สร้างใน backend แล้ว)
        const optionsWithIds = options.filter((option) => option.id);

        if (optionsWithIds.length === 0) {
          return;
        }

        // import OptionService แบบ dynamic เพื่อเลี่ยงปัญหา circular dependencies
        const { OptionService } = await import('src/services/asm/optionService');
        const optionService = new OptionService();

        // เตรียมข้อมูล options สำหรับ API
        const optionsData = optionsWithIds.map((option) => ({
          id: option.id!,
          sequence: option.sequence,
          optionText: option.optionText,
          value: option.score,
        }));

        // เรียก API เพื่ออัปเดตลำดับ
        await optionService.updateOptionSequences(optionsData, id);
      } catch (error) {
        console.error('❌ [STORE] Failed to sync option sequences:', error);
        // ไม่ throw error เพื่อไม่ให้ UI พัง - state local ยังอัปเดตอยู่
      }
    };

    // ฟังก์ชันช่วย sync ลำดับ grid column option กับ backend หลัง drag-and-drop
    const syncGridColumnSequences = async () => {
      try {
        const options = gridColumnOptions.value;

        // sync เฉพาะ options ที่มี ID (สร้างใน backend แล้ว)
        const optionsWithIds = options.filter((option) => option.id);

        if (optionsWithIds.length === 0) {
          return;
        }

        // import OptionService แบบ dynamic เพื่อเลี่ยงปัญหา circular dependencies
        const { OptionService } = await import('src/services/asm/optionService');
        const optionService = new OptionService();

        // เตรียมข้อมูล options สำหรับ API
        const optionsData = optionsWithIds.map((option) => ({
          id: option.id!,
          sequence: option.sequence,
          optionText: option.optionText,
          value: option.score,
        }));

        // เรียก API เพื่ออัปเดตลำดับ
        await optionService.updateOptionSequences(optionsData, id);
      } catch (error) {
        console.error('❌ [STORE] Failed to sync grid column option sequences:', error);
        // ไม่ throw error เพื่อไม่ให้ UI พัง - state local ยังอัปเดตอยู่
      }
    };

    return {
      //state
      radioOptions,
      optionSelectedOption,
      checkboxOptions,
      checkboxSelectedOptions,
      gridRowQuestions,
      gridColumnOptions,
      textInput,
      draggedIndex,
      draggedSection,
      hoveredIndex,

      // Actions
      updateOption,
      addOption,
      addOtherOption,
      removeOption,
      addRowQuestion,
      addColumnOption,
      removeRowQuestion,
      removeColumnOption,
      updateRowQuestion,
      updateColumnOption,
      startDrag,
      handleDragStart,
      hoverRow,
      drop,
      gridDrop,
      endDrag,

      // Extract methods
      extractOptions,
      extractQuestions,
      extractItemBlockData,

      // Helper methods for ID management
      findOptionById,
      updateOptionId,
      syncOptionSequences,
      syncGridColumnSequences,
    };
  });
